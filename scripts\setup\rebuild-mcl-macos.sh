#!/bin/bash

# MCL Library Rebuild Script for macOS
# This script specifically rebuilds the MCL library with proper macOS settings

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    echo -e "${BLUE}[MCL-BUILD]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS only. Current OS: $OSTYPE"
    exit 1
fi

print_message "Rebuilding MCL library for macOS..."

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
print_message "Project root: $PROJECT_ROOT"

# Navigate to MCL directory
cd "$PROJECT_ROOT/mcl"

# Clean previous builds
print_message "Cleaning previous builds..."
rm -rf build
rm -rf lib/*
rm -rf obj/*
rm -rf bin/*

# Detect architecture and set GMP path
ARCH=$(uname -m)
print_message "Detected architecture: $ARCH"

if [[ "$ARCH" == "arm64" ]]; then
    # Apple Silicon (M1/M2)
    GMP_DIR="/opt/homebrew"
    print_message "Using Apple Silicon Homebrew path for GMP: $GMP_DIR"
else
    # Intel Mac
    GMP_DIR="/usr/local"
    print_message "Using Intel Mac Homebrew path for GMP: $GMP_DIR"
fi

# Check if GMP is installed
if [ ! -d "$GMP_DIR/include/gmp.h" ] && [ ! -f "$GMP_DIR/include/gmp.h" ]; then
    print_error "GMP not found at $GMP_DIR. Please install it with: brew install gmp"
    exit 1
fi

# Method 1: Try the provided build script first
print_message "Attempting build using provided build.sh script..."
if ./build.sh; then
    print_success "Build script completed successfully"
else
    print_message "Build script failed, trying manual CMake build..."
    
    # Method 2: Manual CMake build
    print_message "Creating build directory..."
    mkdir -p build
    cd build
    
    print_message "Running CMake configuration..."
    cmake .. \
        -DCMAKE_BUILD_TYPE=Release \
        -DBUILD_TESTING=ON \
        -DMCL_BUILD_SAMPLE=ON \
        -DMCL_USE_LLVM=ON \
        -DCMAKE_INSTALL_PREFIX=./install \
        -DGMP_DIR="$GMP_DIR" \
        -DCMAKE_CXX_COMPILER=clang++ \
        -DCMAKE_C_COMPILER=clang
    
    print_message "Building with make (using all available cores)..."
    make -j$(sysctl -n hw.ncpu)
    
    cd ..
fi

# Method 3: Fallback to Makefile if CMake fails
if [ ! -d "build/lib" ] || [ -z "$(ls -A build/lib 2>/dev/null)" ]; then
    print_message "CMake build failed or produced no libraries. Trying Makefile..."
    
    # Clean again
    rm -rf build lib/* obj/* bin/*
    
    # Use the traditional Makefile
    print_message "Building with Makefile..."
    make clean
    make -j$(sysctl -n hw.ncpu) GMP_DIR="$GMP_DIR"
    
    # Create build/lib directory and copy libraries
    mkdir -p build/lib
    if [ -d "lib" ] && [ "$(ls -A lib 2>/dev/null)" ]; then
        cp lib/* build/lib/
        print_message "Copied libraries from lib/ to build/lib/"
    fi
fi

# Verify the build
print_message "Verifying MCL library build..."

if [ ! -d "build/lib" ]; then
    print_error "build/lib directory not found!"
    exit 1
fi

if [ -z "$(ls -A build/lib 2>/dev/null)" ]; then
    print_error "build/lib directory is empty!"
    exit 1
fi

print_message "Built libraries:"
ls -la build/lib/

# Check for required libraries with proper extensions
REQUIRED_LIBS=("libmcl" "libmclbn384_256")
missing_libs=()

for lib_name in "${REQUIRED_LIBS[@]}"; do
    found=false
    for ext in dylib so; do
        if ls build/lib/${lib_name}.${ext}* 2>/dev/null; then
            print_success "Found ${lib_name}.${ext}"
            found=true
            break
        fi
    done
    if [ "$found" = false ]; then
        missing_libs+=("$lib_name")
    fi
done

if [ ${#missing_libs[@]} -gt 0 ]; then
    print_error "Missing required libraries: ${missing_libs[*]}"
    print_message "Available files in build/lib:"
    ls -la build/lib/ || echo "Directory is empty or doesn't exist"
    exit 1
fi

# Set up environment variable
MCL_LIB_PATH="$PROJECT_ROOT/mcl/build/lib"
print_success "MCL library built successfully!"
print_message "Library path: $MCL_LIB_PATH"

# Export for current session
export MCL_LIB_PATH="$MCL_LIB_PATH"

print_message "To use the libraries, set the environment variable:"
print_message "export MCL_LIB_PATH=\"$MCL_LIB_PATH\""

# Test library loading
print_message "Testing library loading..."
if python3 -c "
import ctypes
import os
lib_path = os.path.join('$MCL_LIB_PATH', 'libmcl.dylib')
if not os.path.exists(lib_path):
    lib_path = os.path.join('$MCL_LIB_PATH', 'libmcl.so')
if os.path.exists(lib_path):
    try:
        ctypes.CDLL(lib_path)
        print('✓ MCL library loads successfully')
    except Exception as e:
        print(f'✗ Error loading MCL library: {e}')
        exit(1)
else:
    print(f'✗ MCL library not found at {lib_path}')
    exit(1)
"; then
    print_success "MCL library test passed!"
else
    print_error "MCL library test failed!"
    exit 1
fi

print_success "MCL rebuild completed successfully!"
