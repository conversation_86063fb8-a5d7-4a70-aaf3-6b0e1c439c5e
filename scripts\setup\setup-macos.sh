#!/bin/bash

# Healthcare Data Sharing - macOS Setup Script
# This script sets up the MCL library and dependencies for macOS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS only. Current OS: $OSTYPE"
    exit 1
fi

print_message "Starting macOS setup for Healthcare Data Sharing project..."

# Check for required tools
print_message "Checking for required tools..."

# Check for Homebrew
if ! command -v brew &> /dev/null; then
    print_error "Homebrew is required but not installed."
    print_message "Please install Homebrew first: https://brew.sh/"
    exit 1
fi
print_success "Homebrew found"

# Check for Python 3
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is required but not installed."
    print_message "Installing Python 3 via Homebrew..."
    brew install python3
fi
print_success "Python 3 found"

# Check for CMake
if ! command -v cmake &> /dev/null; then
    print_message "CMake not found. Installing via Homebrew..."
    brew install cmake
fi
print_success "CMake found"

# Check for GMP (GNU Multiple Precision Arithmetic Library)
if ! brew list gmp &> /dev/null; then
    print_message "GMP not found. Installing via Homebrew..."
    brew install gmp
fi
print_success "GMP found"

# Check for clang++
if ! command -v clang++ &> /dev/null; then
    print_error "clang++ is required but not found."
    print_message "Please install Xcode Command Line Tools: xcode-select --install"
    exit 1
fi
print_success "clang++ found"

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
print_message "Project root: $PROJECT_ROOT"

# Navigate to project root
cd "$PROJECT_ROOT"

# Create necessary directories
print_message "Creating necessary directories..."
mkdir -p local_storage/records
mkdir -p local_storage/purchases
mkdir -p local_storage/transactions
mkdir -p local_storage/store_transactions
mkdir -p local_storage/share_transactions
mkdir -p local_storage/purchase_transactions
mkdir -p keys

# Check if .env file exists, create if not
if [ ! -f .env ]; then
    print_message "Creating .env file with default values..."
    cat > .env << 'EOL'
# BASE Sepolia testnet connection
SEPOLIA_RPC_URL=https://base-sepolia-rpc.publicnode.com
CONTRACT_ADDRESS=0x8Cbf9a04C9c7F329DCcaeabE90a424e8F9687aaA
# Deployed on BASE Sepolia

# Account addresses and private keys
PATIENT_ADDRESS=0x742d35Cc6634C0532925a3b8D4C9db96c4b5Da5A
DOCTOR_ADDRESS=0x8ba1f109551bD432803012645Hac136c30C6A0
HOSPITAL_ADDRESS=0x4B20993Bc481177ec7E8f571ceCaE8A9e22C02db
GROUP_MANAGER_ADDRESS=0x78731D3Ca6b7E34aC0F824c42a7cC18A495cabaB
REVOCATION_MANAGER_ADDRESS=0x617F2E2fD72FD9D5503197092aC168c91465E7f2
BUYER_ADDRESS=0x17F6AD8Ef982297579C203069C1DbfFE4348c372

# API Keys
BASESCAN_API_KEY=I61T8UZK7YKRC8P61BHF6237PG9GC2VK3Y
EOL
    print_success ".env file created successfully."
else
    print_message ".env file already exists, skipping creation."
fi

# Build MCL library for macOS
print_message "Building MCL library for macOS..."

# Navigate to MCL directory
cd mcl

# Clean previous builds
print_message "Cleaning previous MCL builds..."
rm -rf build
rm -rf lib/*
rm -rf obj/*

# Detect architecture
ARCH=$(uname -m)
print_message "Detected architecture: $ARCH"

# Set GMP directory for Homebrew
if [[ "$ARCH" == "arm64" ]]; then
    # Apple Silicon (M1/M2)
    GMP_DIR="/opt/homebrew"
    print_message "Using Apple Silicon Homebrew path for GMP"
else
    # Intel Mac
    GMP_DIR="/usr/local"
    print_message "Using Intel Mac Homebrew path for GMP"
fi

# Build using the provided build script
print_message "Running MCL build script..."
if ! ./build.sh; then
    print_error "MCL build script failed. Trying manual build..."
    
    # Manual build as fallback
    print_message "Attempting manual CMake build..."
    mkdir -p build
    cd build
    
    cmake .. \
        -DCMAKE_BUILD_TYPE=Release \
        -DBUILD_TESTING=ON \
        -DMCL_BUILD_SAMPLE=ON \
        -DMCL_USE_LLVM=ON \
        -DCMAKE_INSTALL_PREFIX=./install \
        -DGMP_DIR="$GMP_DIR"
    
    make -j$(sysctl -n hw.ncpu)
    cd ..
fi

# Verify that the libraries were built
print_message "Verifying MCL library build..."
if [ ! -d "build/lib" ]; then
    print_error "MCL build/lib directory not found!"
    exit 1
fi

# List built libraries
print_message "Built libraries:"
ls -la build/lib/

# Check for required libraries
REQUIRED_LIBS=("libmcl" "libmclbn384_256")
for lib_name in "${REQUIRED_LIBS[@]}"; do
    found=false
    for ext in dylib so; do
        if ls build/lib/${lib_name}.${ext}* 2>/dev/null; then
            print_success "Found ${lib_name}.${ext}"
            found=true
            break
        fi
    done
    if [ "$found" = false ]; then
        print_error "Required library ${lib_name} not found!"
        exit 1
    fi
done

# Set MCL_LIB_PATH environment variable
MCL_LIB_PATH="$PROJECT_ROOT/mcl/build/lib"
print_message "Setting MCL_LIB_PATH to: $MCL_LIB_PATH"

# Add to shell profile if not already present
SHELL_PROFILE=""
if [ -f "$HOME/.zshrc" ]; then
    SHELL_PROFILE="$HOME/.zshrc"
elif [ -f "$HOME/.bash_profile" ]; then
    SHELL_PROFILE="$HOME/.bash_profile"
elif [ -f "$HOME/.bashrc" ]; then
    SHELL_PROFILE="$HOME/.bashrc"
fi

if [ -n "$SHELL_PROFILE" ]; then
    if ! grep -q "MCL_LIB_PATH" "$SHELL_PROFILE"; then
        print_message "Adding MCL_LIB_PATH to $SHELL_PROFILE"
        echo "" >> "$SHELL_PROFILE"
        echo "# Healthcare Data Sharing - MCL Library Path" >> "$SHELL_PROFILE"
        echo "export MCL_LIB_PATH=\"$MCL_LIB_PATH\"" >> "$SHELL_PROFILE"
        print_success "MCL_LIB_PATH added to $SHELL_PROFILE"
    else
        print_message "MCL_LIB_PATH already exists in $SHELL_PROFILE"
    fi
fi

# Export for current session
export MCL_LIB_PATH="$MCL_LIB_PATH"

# Navigate back to project root
cd "$PROJECT_ROOT"

print_success "macOS setup completed successfully!"
print_message "Next steps:"
print_message "1. Restart your terminal or run: source $SHELL_PROFILE"
print_message "2. Activate your Python virtual environment"
print_message "3. Install Python dependencies: pip install -r requirements.txt"
print_message "4. Start the FastAPI backend: ./scripts/development/run_refactored_api.sh"

print_warning "Note: If you encounter any issues, check that MCL_LIB_PATH is set correctly:"
print_warning "echo \$MCL_LIB_PATH"
print_warning "ls \$MCL_LIB_PATH"
