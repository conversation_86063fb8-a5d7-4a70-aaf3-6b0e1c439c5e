# macOS Setup Guide for Healthcare Data Sharing

This guide will help you resolve the MCL library loading issue and get the FastAPI backend running on macOS.

## Problem Description

The error you're encountering occurs because:
1. The MCL library was built in a Linux environment (creating `.so` files)
2. macOS requires `.dylib` files for dynamic libraries
3. The pygroupsig module was hardcoded to look for `.so` files

## Solution Overview

We've implemented a cross-platform solution that:
1. Automatically detects your operating system
2. Uses the appropriate library extension (`.dylib` for macOS)
3. Provides fallback mechanisms to find libraries
4. Includes macOS-specific build scripts

## Prerequisites

Before starting, ensure you have:

1. **Homebrew** installed: https://brew.sh/
2. **Xcode Command Line Tools**: `xcode-select --install`
3. **Python 3** (will be installed via Homebrew if missing)

## Step-by-Step Solution

### Step 1: Install Required Dependencies

```bash
# Install Homebrew (if not already installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required packages
brew install cmake gmp python3
```

### Step 2: Run the macOS Setup Script

We've created a comprehensive setup script that handles everything:

```bash
# Navigate to your project directory
cd /Users/<USER>/Documents/GitHub/healthcare-data-sharing

# Run the macOS setup script
./scripts/setup/setup-macos.sh
```

This script will:
- Check for all required dependencies
- Build the MCL library with proper macOS settings
- Create necessary directories
- Set up environment variables
- Generate a `.env` file if needed

### Step 3: Alternative - Manual MCL Rebuild

If the setup script encounters issues, you can rebuild just the MCL library:

```bash
# Run the MCL-specific rebuild script
./scripts/setup/rebuild-mcl-macos.sh
```

### Step 4: Set Environment Variables

After building, ensure the MCL library path is set:

```bash
# Add to your shell profile (the setup script does this automatically)
export MCL_LIB_PATH="/Users/<USER>/Documents/GitHub/healthcare-data-sharing/mcl/build/lib"

# For permanent setup, add to ~/.zshrc (or ~/.bash_profile)
echo 'export MCL_LIB_PATH="/Users/<USER>/Documents/GitHub/healthcare-data-sharing/mcl/build/lib"' >> ~/.zshrc
```

### Step 5: Verify the Build

Check that the libraries were built correctly:

```bash
# List the built libraries
ls -la mcl/build/lib/

# You should see files like:
# libmcl.dylib (or libmcl.so)
# libmclbn384_256.dylib (or libmclbn384_256.so)
```

### Step 6: Test the Fix

Try importing pygroupsig to verify the fix:

```bash
# Activate your virtual environment first
source venv/bin/activate  # or however you activate your venv

# Test the import
python3 -c "
import sys
sys.path.append('.')
from pygroupsig import group
print('✓ pygroupsig imported successfully!')
"
```

### Step 7: Start the FastAPI Backend

```bash
# Make sure you're in the project root and venv is activated
./scripts/development/run_refactored_api.sh
```

## Troubleshooting

### Issue: "brew command not found"
**Solution**: Install Homebrew first: https://brew.sh/

### Issue: "clang++ not found"
**Solution**: Install Xcode Command Line Tools:
```bash
xcode-select --install
```

### Issue: Libraries still not found
**Solution**: 
1. Check the MCL_LIB_PATH environment variable:
   ```bash
   echo $MCL_LIB_PATH
   ls $MCL_LIB_PATH
   ```
2. Rebuild the MCL library:
   ```bash
   ./scripts/setup/rebuild-mcl-macos.sh
   ```

### Issue: Permission denied on scripts
**Solution**: Make scripts executable:
```bash
chmod +x scripts/setup/setup-macos.sh
chmod +x scripts/setup/rebuild-mcl-macos.sh
```

### Issue: GMP library not found
**Solution**: 
```bash
brew install gmp
# For Apple Silicon Macs, GMP will be in /opt/homebrew/
# For Intel Macs, GMP will be in /usr/local/
```

## Architecture-Specific Notes

### Apple Silicon (M1/M2) Macs
- GMP libraries are installed to `/opt/homebrew/`
- The build scripts automatically detect this

### Intel Macs
- GMP libraries are installed to `/usr/local/`
- The build scripts automatically detect this

## What We Fixed

1. **Cross-platform library detection**: Modified `pygroupsig/utils/constants.py` to automatically detect the platform and use appropriate file extensions
2. **Fallback mechanisms**: Added logic to try alternative extensions if the expected ones aren't found
3. **Better error messages**: Enhanced debugging output to help identify issues
4. **macOS-specific build scripts**: Created scripts that handle macOS-specific build requirements

## Files Modified/Created

- `pygroupsig/utils/constants.py` - Added cross-platform support
- `scripts/setup/setup-macos.sh` - Complete macOS setup script
- `scripts/setup/rebuild-mcl-macos.sh` - MCL-specific rebuild script
- `MACOS_SETUP.md` - This documentation

## Next Steps

After completing this setup:
1. The FastAPI backend should start without MCL library errors
2. All pygroupsig functionality should work correctly
3. You can proceed with testing the healthcare data sharing workflows

If you encounter any issues, the enhanced error messages in the updated code will provide more detailed information about what's going wrong.
