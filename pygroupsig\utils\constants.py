import ctypes
import os
import sys
import platform

# src/bn_c384_256.cpp
MCLBN_FP_UNIT_SIZE: int = 6
MCLBN_FR_UNIT_SIZE: int = 4

# include/lib/curve_type.h
MCL_BLS12_381: int = 5

# include/lib/bn.h
MCLBN_COMPILED_TIME_VAR: int = MCLBN_FR_UNIT_SIZE * 10 + MCLBN_FP_UNIT_SIZE

# src/shim/pbc_ext.h (libgroupsig)
BLS12_381_P: str = "1 3685416753713387016781088315183077757961620795782546409894578378688607592378376318836054947676345821548104185464507 1339506544944476473020471379941921221584933875938349620426543736416511423956333506472724655353366534992391756441569"
BLS12_381_Q: str = "1 352701069587466618187139116011060144890029952792775240219908644239793785735715026873347600343865175952761926303160 3059144344244213709971259814753781636986470325476647558659373206291635324768958432433509563104347017837885763365758 1985150602287291935568054521177171638300868978215655730859378665066344726373823718423869104263333984641494340347905 927553665492332455747201965776037880757740193453592970025027978793976877002675564980949289727957565575433344219582"

# Detect platform and set appropriate library extensions
def get_library_extension():
    """Get the appropriate library extension for the current platform."""
    system = platform.system()
    if system == 'Linux':
        return 'so'
    elif system == 'Darwin':  # macOS
        return 'dylib'
    elif system == 'Windows':
        return 'dll'
    else:
        # Default to .so for unknown platforms
        print(f"Warning: Unknown platform {system}, defaulting to .so extension")
        return 'so'

# Get the absolute path to the mcl/build/lib directory
# For Windows paths in WSL, we need to convert from /mnt/c/... to C:\...
if os.path.exists('/mnt/c'):
    # We're in WSL
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    mcl_build_lib = os.path.join(project_root, "mcl", "build", "lib")
    print(f"Project root: {project_root}")
    print(f"MCL build lib path: {mcl_build_lib}")
else:
    # We're in a regular Linux/macOS/Windows environment
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    mcl_build_lib = os.path.join(project_root, "mcl", "build", "lib")

# Set library names based on platform
lib_ext = get_library_extension()
LIB_PATH: str = os.environ.get("MCL_LIB_PATH", mcl_build_lib)
MCL_LIB: str = f"libmcl.{lib_ext}"
MCL384_LIB: str = f"libmclbn384_256.{lib_ext}"
lib: ctypes.CDLL | None = None


def load_library() -> None:
    global lib
    if not LIB_PATH:
        raise RuntimeError("Environment variable MCL_LIB_PATH missing.")

    print(f"Platform detected: {platform.system()}")
    print(f"Library extension: {lib_ext}")
    print(f"Loading MCL libraries from: {LIB_PATH}")
    print(f"Looking for: {MCL_LIB} and {MCL384_LIB}")

    try:
        # Check if the library files exist
        mcl_lib_path = os.path.join(LIB_PATH, MCL_LIB)
        mcl384_lib_path = os.path.join(LIB_PATH, MCL384_LIB)

        # Try to find libraries with different extensions if the expected ones don't exist
        if not os.path.exists(mcl_lib_path):
            print(f"Warning: {mcl_lib_path} does not exist")
            # Try alternative extensions
            for alt_ext in ['so', 'dylib', 'dll']:
                if alt_ext != lib_ext:
                    alt_mcl_lib = f"libmcl.{alt_ext}"
                    alt_path = os.path.join(LIB_PATH, alt_mcl_lib)
                    if os.path.exists(alt_path):
                        print(f"Found alternative: {alt_path}")
                        mcl_lib_path = alt_path
                        break
            else:
                # Try to find the library in the system
                for path in sys.path:
                    potential_path = os.path.join(path, MCL_LIB)
                    if os.path.exists(potential_path):
                        print(f"Found {MCL_LIB} at {potential_path}")
                        mcl_lib_path = potential_path
                        break

        if not os.path.exists(mcl384_lib_path):
            print(f"Warning: {mcl384_lib_path} does not exist")
            # Try alternative extensions
            for alt_ext in ['so', 'dylib', 'dll']:
                if alt_ext != lib_ext:
                    alt_mcl384_lib = f"libmclbn384_256.{alt_ext}"
                    alt_path = os.path.join(LIB_PATH, alt_mcl384_lib)
                    if os.path.exists(alt_path):
                        print(f"Found alternative: {alt_path}")
                        mcl384_lib_path = alt_path
                        break
            else:
                # Try to find the library in the system
                for path in sys.path:
                    potential_path = os.path.join(path, MCL384_LIB)
                    if os.path.exists(potential_path):
                        print(f"Found {MCL384_LIB} at {potential_path}")
                        mcl384_lib_path = potential_path
                        break

        # Final check before loading
        if not os.path.exists(mcl_lib_path):
            raise FileNotFoundError(f"MCL library not found: {mcl_lib_path}")
        if not os.path.exists(mcl384_lib_path):
            raise FileNotFoundError(f"MCL384 library not found: {mcl384_lib_path}")

        print(f"Loading {mcl_lib_path}")
        ctypes.CDLL(mcl_lib_path)
        print(f"Loading {mcl384_lib_path}")
        lib = ctypes.CDLL(mcl384_lib_path)

        print("Initializing MCL library...")
        if lib.mclBn_init(MCL_BLS12_381, MCLBN_COMPILED_TIME_VAR):
            raise RuntimeError("mcl library could not be initialized")
        print("MCL library loaded and initialized successfully!")

    except Exception as e:
        print(f"Error loading MCL libraries: {e}")
        print(f"Current working directory: {os.getcwd()}")
        print(f"Library path contents: {os.listdir(LIB_PATH) if os.path.exists(LIB_PATH) else 'Directory does not exist'}")
        raise
